package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;
import com.ruoyi.hgz.miitvipws.client.ObjectFactory;

import javax.net.ssl.*;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Unmarshaller;
import javax.xml.namespace.QName;
import javax.xml.soap.*;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;
import javax.xml.ws.soap.SOAPBinding;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;

/**
 * 智能WebService客户端
 * 自动检测响应格式并选择最佳解析策略
 * 优先使用标准JAXB映射，失败时自动降级到文本解析
 */
public class SmartWSClient {
    
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    private static volatile CertificateRequestVIP standardPort;
    private static volatile Dispatch<SOAPMessage> soapDispatch;
    private static volatile JAXBContext jaxbContext;
    private static volatile boolean sslInitialized = false;
    
    /**
     * 初始化SSL配置
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }
        
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
            
            sslInitialized = true;
            System.out.println("SmartWSClient SSL配置完成");
        } catch (Exception e) {
            System.err.println("SmartWSClient SSL配置失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 获取标准WebService端口（优先方案）
     */
    private static synchronized CertificateRequestVIP getStandardPort() throws Exception {
        if (standardPort == null) {
            try {
                initSSL();
                
                // 尝试使用WSDL创建标准客户端
                String wsdlLocation = SmartWSClient.class.getResource("/com/ruoyi/hgz/miitvipws/CertificateRequestVIPService.wsdl").toString();
                URL wsdlUrl = new URL(wsdlLocation);
                QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
                
                Service service = Service.create(wsdlUrl, serviceQName);
                standardPort = service.getPort(CertificateRequestVIP.class);
                
                // 设置端点地址
                ((javax.xml.ws.BindingProvider) standardPort).getRequestContext()
                    .put(javax.xml.ws.BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);
                
                System.out.println("SmartWSClient 标准端口创建成功");
            } catch (Exception e) {
                System.out.println("SmartWSClient 标准端口创建失败，将使用备用方案: " + e.getMessage());
                standardPort = null;
                throw e;
            }
        }
        return standardPort;
    }
    
    /**
     * 获取SOAP Dispatch客户端（备用方案）
     */
    private static synchronized Dispatch<SOAPMessage> getSOAPDispatch() throws Exception {
        if (soapDispatch == null) {
            initSSL();
            
            QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
            QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
            
            Service service = Service.create(serviceQName);
            service.addPort(portQName, SOAPBinding.SOAP11HTTP_BINDING, SERVICE_URL);
            
            soapDispatch = service.createDispatch(portQName, SOAPMessage.class, Service.Mode.MESSAGE);
            
            System.out.println("SmartWSClient SOAP Dispatch创建成功");
        }
        return soapDispatch;
    }
    
    /**
     * HelloWorld方法 - 智能调用
     */
    public static String helloWorld() {
        // 策略1：尝试标准端口
        try {
            CertificateRequestVIP port = getStandardPort();
            String result = port.helloWorld();
            System.out.println("SmartWSClient HelloWorld使用标准端口成功");
            return result;
        } catch (Exception e) {
            System.out.println("SmartWSClient 标准端口调用失败: " + e.getMessage());
        }
        
        // 策略2：使用SOAP Dispatch
        try {
            SOAPMessage request = createHelloWorldSOAPMessage();
            Dispatch<SOAPMessage> dispatch = getSOAPDispatch();
            SOAPMessage response = dispatch.invoke(request);
            
            String result = extractTextFromSOAPMessage(response);
            System.out.println("SmartWSClient HelloWorld使用SOAP Dispatch成功");
            return result;
        } catch (Exception e) {
            throw new RuntimeException("SmartWSClient HelloWorld所有策略都失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询证书信息 - 智能调用
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        System.out.println("SmartWSClient 开始智能查询...");
        
        // 策略1：尝试标准JAXB映射
        List<CertificateInfo> result = tryStandardQuery(username, password, wzhgzbh, clsbdh);
        if (result != null && !result.isEmpty()) {
            System.out.println("SmartWSClient 标准查询成功，返回 " + result.size() + " 条记录");
            return result;
        }
        
        // 策略2：使用SOAP Dispatch + 智能解析
        result = trySOAPQuery(username, password, wzhgzbh, clsbdh);
        if (result != null && !result.isEmpty()) {
            System.out.println("SmartWSClient SOAP查询成功，返回 " + result.size() + " 条记录");
            return result;
        }
        
        // 策略3：使用DynamicWSClient作为最后备用
        result = tryDynamicQuery(username, password, wzhgzbh, clsbdh);
        if (result != null && !result.isEmpty()) {
            System.out.println("SmartWSClient 动态查询成功，返回 " + result.size() + " 条记录");
            return result;
        }
        
        System.out.println("SmartWSClient 所有查询策略都失败");
        return new ArrayList<>();
    }
    
    /**
     * 策略1：标准JAXB映射查询
     */
    private static List<CertificateInfo> tryStandardQuery(String username, String password, 
                                                          String wzhgzbh, String clsbdh) {
        try {
            CertificateRequestVIP port = getStandardPort();
            List<CertificateInfo> result = port.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            
            // 验证结果的有效性
            if (result != null && !result.isEmpty()) {
                for (CertificateInfo cert : result) {
                    if (cert != null && hasValidData(cert)) {
                        return result; // 找到有效数据
                    }
                }
            }
            
            return null; // 没有有效数据
            
        } catch (Exception e) {
            System.out.println("SmartWSClient 标准查询失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 策略2：SOAP Dispatch查询
     */
    private static List<CertificateInfo> trySOAPQuery(String username, String password, 
                                                      String wzhgzbh, String clsbdh) {
        try {
            SOAPMessage request = createQuerySOAPMessage(username, password, wzhgzbh, clsbdh);
            Dispatch<SOAPMessage> dispatch = getSOAPDispatch();
            SOAPMessage response = dispatch.invoke(request);
            
            // 使用DirectWSClient的智能解析逻辑
            return DirectWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            
        } catch (Exception e) {
            System.out.println("SmartWSClient SOAP查询失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 策略3：动态查询（最后备用）
     */
    private static List<CertificateInfo> tryDynamicQuery(String username, String password, 
                                                         String wzhgzbh, String clsbdh) {
        try {
            return FixedWSClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        } catch (Exception e) {
            System.out.println("SmartWSClient 动态查询失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证CertificateInfo是否包含有效数据
     */
    private static boolean hasValidData(CertificateInfo cert) {
        return cert != null && (
            (cert.getWZHGZBH() != null && cert.getWZHGZBH().getValue() != null && !cert.getWZHGZBH().getValue().trim().isEmpty()) ||
            (cert.getCLSBDH() != null && cert.getCLSBDH().getValue() != null && !cert.getCLSBDH().getValue().trim().isEmpty()) ||
            (cert.getCLZZQYMC() != null && cert.getCLZZQYMC().getValue() != null && !cert.getCLZZQYMC().getValue().trim().isEmpty())
        );
    }
    
    /**
     * 创建HelloWorld SOAP消息
     */
    private static SOAPMessage createHelloWorldSOAPMessage() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPBody body = envelope.getBody();
        SOAPElement helloWorldElement = body.addChildElement("HelloWorld", "tns");
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 创建查询SOAP消息
     */
    private static SOAPMessage createQuerySOAPMessage(String username, String password, 
                                                      String wzhgzbh, String clsbdh) throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPBody body = envelope.getBody();
        SOAPElement queryElement = body.addChildElement("QueryCertificateSingle", "tns");
        
        // 添加参数
        SOAPElement usernameElement = queryElement.addChildElement("username", "tns");
        usernameElement.addTextNode(username);
        
        SOAPElement passwordElement = queryElement.addChildElement("password", "tns");
        passwordElement.addTextNode(password);
        
        SOAPElement wzhgzbhElement = queryElement.addChildElement("wzhgzbh", "tns");
        wzhgzbhElement.addTextNode(wzhgzbh);
        
        SOAPElement clsbdhElement = queryElement.addChildElement("clsbdh", "tns");
        clsbdhElement.addTextNode(clsbdh);
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 从SOAP消息中提取文本内容
     */
    private static String extractTextFromSOAPMessage(SOAPMessage message) throws Exception {
        SOAPBody body = message.getSOAPBody();
        StringBuilder result = new StringBuilder();
        extractTextFromNode(body, result);
        return result.toString().trim();
    }
    
    /**
     * 递归提取节点中的文本内容
     */
    private static void extractTextFromNode(org.w3c.dom.Node node, StringBuilder result) {
        if (node.getNodeType() == org.w3c.dom.Node.TEXT_NODE) {
            String text = node.getNodeValue().trim();
            if (!text.isEmpty()) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(text);
            }
        } else if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
            org.w3c.dom.NodeList children = node.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                extractTextFromNode(children.item(i), result);
            }
        }
    }
    
    /**
     * 重置所有连接
     */
    public static synchronized void resetConnection() {
        standardPort = null;
        soapDispatch = null;
        DirectWSClient.resetConnection();
        FixedWSClient.resetConnection();
        System.out.println("SmartWSClient 所有连接已重置");
    }
    
    /**
     * 验证连接
     */
    public static boolean validateConnection() {
        try {
            String result = helloWorld();
            return result != null && result.contains("Hello");
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取服务信息
     */
    public static String getServiceInfo() {
        return "SmartWSClient - 智能WebService客户端\n" +
               "自动检测响应格式并选择最佳解析策略\n" +
               "策略1: 标准JAXB映射 (最可靠)\n" +
               "策略2: SOAP Dispatch + 智能解析 (兼容性好)\n" +
               "策略3: 动态客户端 (最后备用)\n" +
               "优先使用实体对象映射，失败时自动降级";
    }
}
