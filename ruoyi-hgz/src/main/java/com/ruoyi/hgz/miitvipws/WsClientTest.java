package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;

import java.util.List;

/**
 * WsClient测试类
 * 用于验证统一WebService客户端的功能
 * 
 * <AUTHOR>
 */
public class WsClientTest {
    
    public static void main(String[] args) {
        System.out.println("=== WsClient 功能测试 ===");
        
        // 测试连接
        testConnection();
        
        // 测试查询功能
        testQueryCertificate();
    }
    
    /**
     * 测试连接功能
     */
    private static void testConnection() {
        System.out.println("\n1. 测试连接功能...");
        try {
            String result = WsClient.testConnection();
            System.out.println("✓ 连接测试成功: " + result);
        } catch (Exception e) {
            System.err.println("✗ 连接测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试查询证书功能
     */
    private static void testQueryCertificate() {
        System.out.println("\n2. 测试查询证书功能...");
        
        // 测试参数（请根据实际情况修改）
        String username = "HX231008U001";
        String password = "D#$>sy38";
        String wzhgzbh = "";
        String clsbdh = "LGHY5J2G2RC053149";
        
        try {
            System.out.println("查询参数:");
            System.out.println("  用户名: " + username);
            System.out.println("  密码: " + password);
            System.out.println("  网证合格证编号: " + (wzhgzbh.isEmpty() ? "(空)" : wzhgzbh));
            System.out.println("  车辆识别代号: " + clsbdh);
            
            List<CertificateInfo> results = WsClient.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
            
            if (results != null && !results.isEmpty()) {
                System.out.println("✓ 查询成功，返回 " + results.size() + " 条记录");
                
                // 显示第一条记录的部分信息
                CertificateInfo firstCert = results.get(0);
                System.out.println("第一条记录信息:");
                
                if (firstCert.getWZHGZBH() != null && firstCert.getWZHGZBH().getValue() != null) {
                    System.out.println("  网证合格证编号: " + firstCert.getWZHGZBH().getValue());
                }
                
                if (firstCert.getCLSBDH() != null && firstCert.getCLSBDH().getValue() != null) {
                    System.out.println("  车辆识别代号: " + firstCert.getCLSBDH().getValue());
                }
                
                if (firstCert.getCLMC() != null && firstCert.getCLMC().getValue() != null) {
                    System.out.println("  车辆名称: " + firstCert.getCLMC().getValue());
                }
                
            } else {
                System.out.println("✓ 查询成功，但未找到匹配的记录");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
