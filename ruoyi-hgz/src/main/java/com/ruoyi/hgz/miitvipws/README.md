# WebService Client Usage Guide

## Overview

The `WsClient` class provides a unified interface for accessing the certificate WebService. It wraps the `ProductionWSClient` to provide a simple and consistent API.

## Features

- **Unified API**: Simple static methods for all WebService operations
- **Connection Management**: Automatic connection handling and reuse
- **Error Handling**: Consistent exception handling across all methods
- **UTF-8 Encoding**: All files use UTF-8 encoding for better compatibility

## Usage Examples

### 1. Test Connection

```java
try {
    String result = WsClient.testConnection();
    System.out.println("Connection test result: " + result);
} catch (RuntimeException e) {
    System.err.println("Connection failed: " + e.getMessage());
}
```

### 2. Query Single Certificate

```java
try {
    String username = "your_username";
    String password = "your_password";
    String wzhgzbh = "certificate_number";  // Can be empty
    String clsbdh = "vehicle_id";
    
    List<CertificateInfo> results = WsClient.queryCertificateSingle(
        username, password, wzhgzbh, clsbdh);
    
    if (!results.isEmpty()) {
        CertificateInfo cert = results.get(0);
        // Process certificate information
        System.out.println("Found certificate: " + 
            cert.getWZHGZBH().getValue());
    }
} catch (RuntimeException e) {
    System.err.println("Query failed: " + e.getMessage());
}
```

### 3. Query by Conditions

```java
try {
    List<CertificateInfo> results = WsClient.queryByCondition(
        username, password, wzhgzbh, clsbdh, clxh,
        status, startTime, endTime, pageNumber, pageSize);
    
    System.out.println("Found " + results.size() + " certificates");
} catch (RuntimeException e) {
    System.err.println("Conditional query failed: " + e.getMessage());
}
```

### 4. Connection Management

```java
// Check connection status
boolean isConnected = WsClient.isConnected();

// Validate connection
boolean isValid = WsClient.validateConnection();

// Reset connection (useful after network issues)
WsClient.resetConnection();

// Get service information
String serviceInfo = WsClient.getServiceInfo();
```

## Integration with HgzToolServiceImpl

The `HgzToolServiceImpl` class has been updated to use `WsClient`:

```java
@Override
public WzHgzInfo getWzHgzInfo(String wzhgzbh) {
    try {
        List<CertificateInfo> list = WsClient.queryCertificateSingle(ycyh, ycmm, wzhgzbh, "");
        
        if (list.size() > 0) {
            CertificateInfo certificateInfo = list.get(0);
            return HgzConvertUtils.convertToWzHgzInfo(certificateInfo);
        }
        
        WzHgzInfo wzHgzInfo = new WzHgzInfo();
        wzHgzInfo.setMsg("【"+wzhgzbh+"】在国家不存在");
        return wzHgzInfo;
    } catch (Exception e) {
        WzHgzInfo wzHgzInfo = new WzHgzInfo();
        wzHgzInfo.setMsg("国家接口网络连接异常，请稍后重试");
        return wzHgzInfo;
    }
}
```

## Benefits

1. **Simplified API**: Direct method calls without complex setup
2. **Consistent Error Handling**: All methods throw `RuntimeException` with descriptive messages
3. **Connection Reuse**: Automatic connection pooling and management
4. **UTF-8 Support**: Better character encoding support
5. **Maintainability**: Single point of configuration and updates

## Testing

Run the `WsClientTest` class to verify functionality:

```bash
java com.ruoyi.hgz.miitvipws.WsClientTest
```

## Notes

- The `WsClient` internally uses `ProductionWSClient` which has proven to be stable
- All SSL certificates are trusted for internal service communication
- Connection timeouts are set to 30 seconds for connection and 60 seconds for requests
- The client automatically handles WSDL dependency issues using dynamic service creation
