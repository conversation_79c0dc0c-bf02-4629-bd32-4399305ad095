# WebService 客户端使用说明

## 问题描述

原始错误：
```
javax.xml.ws.WebServiceException: WSDL 元数据无法用于创建代理, 服务实例或 ServiceEndpointInterface com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP应包含 WSDL 信息
```

## 解决方案

### 1. 问题原因
- WSDL 文件位置不正确
- 生成的客户端代码依赖远程 WSDL URL
- 接口注解缺少必要的 wsdlLocation 属性

### 2. 修复内容
- ✅ 将 WSDL 文件移动到正确的 resources 目录
- ✅ 修改客户端代码使用动态方式创建服务
- ✅ 添加完善的 SSL 配置和错误处理
- ✅ 提供多种客户端实现方案

## 客户端类说明

### ProductionWSClient（推荐使用）
生产环境推荐的客户端，具有以下特点：
- 动态创建服务，避免 WSDL 依赖问题
- 完善的 SSL 配置
- 连接池和超时管理
- 详细的错误处理

### DynamicWSClient
使用原生 SOAP 消息的客户端：
- 完全避开生成的代码
- 直接操作 SOAP 消息
- 最大的灵活性和控制力

### WSClient（已修复）
原始客户端的修复版本：
- 保持原有接口不变
- 修复 WSDL 加载问题
- 向后兼容

## 使用示例

### 基本连接测试
```java
// 测试连接
String result = ProductionWSClient.testConnection();
System.out.println("连接测试结果: " + result);
```

### 查询证书信息
```java
// 查询单个证书
List<CertificateInfo> results = ProductionWSClient.queryCertificateSingle(
    "用户名", "密码", "", "车辆识别代号");

if (results != null && !results.isEmpty()) {
    for (CertificateInfo cert : results) {
        System.out.println("证书编号: " + cert.getWZHGZBH().getValue());
        System.out.println("车辆识别代号: " + cert.getCLSBDH().getValue());
    }
}
```

### 按条件查询
```java
// 按条件查询
List<CertificateInfo> results = ProductionWSClient.queryByCondition(
    "用户名", "密码", "证书编号", "车辆识别代号", "车辆型号",
    "状态", "开始时间", "结束时间", 1, 10);
```

### 错误处理
```java
try {
    List<CertificateInfo> results = ProductionWSClient.queryCertificateSingle(
        username, password, wzhgzbh, clsbdh);
    // 处理结果
} catch (RuntimeException e) {
    System.err.println("查询失败: " + e.getMessage());
    // 可以尝试重置连接
    ProductionWSClient.resetConnection();
}
```

## 配置说明

### 超时设置
- 连接超时：30秒
- 请求超时：60秒

### SSL 配置
- 自动信任所有 SSL 证书
- 跳过主机名验证
- 适用于内网环境

### 服务地址
```
https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService
```

## 测试结果

所有客户端都已通过测试：
- ✅ HelloWorld 方法调用成功
- ✅ 证书查询功能正常
- ✅ 返回完整的证书数据

## 注意事项

1. **网络环境**：确保服务器能访问目标 WebService
2. **认证信息**：使用正确的用户名和密码
3. **参数格式**：车辆识别代号等参数格式要正确
4. **异常处理**：建议捕获异常并进行适当的重试机制

## 故障排除

### 连接超时
```java
// 检查连接状态
if (!ProductionWSClient.validateConnection()) {
    ProductionWSClient.resetConnection();
}
```

### SSL 证书问题
客户端已自动处理 SSL 证书验证，如仍有问题请检查网络配置。

### WSDL 加载失败
使用 ProductionWSClient 或 DynamicWSClient，它们不依赖 WSDL 文件。

## 版本历史

- v1.0：解决 WSDL 元数据问题，提供多种客户端实现
- 测试通过：2025-06-28

## 联系支持

如有问题请联系开发团队或查看项目文档。
