package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.ObjectFactory;

import javax.net.ssl.*;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.namespace.QName;
import javax.xml.soap.*;
import javax.xml.transform.dom.DOMSource;
import javax.xml.ws.Dispatch;
import javax.xml.ws.Service;
import javax.xml.ws.soap.SOAPBinding;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;

/**
 * 直接返回实体对象的WebService客户端
 * 使用JAXB直接处理对象序列化和反序列化
 */
public class DirectWSClient {
    
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    private static volatile Dispatch<SOAPMessage> dispatch;
    private static volatile JAXBContext jaxbContext;
    private static volatile boolean sslInitialized = false;
    
    /**
     * 初始化SSL配置
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }
        
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };
            
            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 创建主机名验证器
            HostnameVerifier allHostsValid = (hostname, session) -> true;
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            
            sslInitialized = true;
            System.out.println("DirectWSClient SSL配置初始化成功");
        } catch (Exception e) {
            System.err.println("DirectWSClient SSL配置初始化失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 初始化JAXB上下文
     */
    private static synchronized void initJAXBContext() throws Exception {
        if (jaxbContext == null) {
            jaxbContext = JAXBContext.newInstance(CertificateInfo.class, ObjectFactory.class);
        }
    }
    
    /**
     * 获取Dispatch客户端
     */
    private static synchronized Dispatch<SOAPMessage> getDispatch() throws Exception {
        if (dispatch == null) {
            initSSL();
            
            QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
            QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
            
            Service service = Service.create(serviceQName);
            service.addPort(portQName, SOAPBinding.SOAP11HTTP_BINDING, SERVICE_URL);
            
            dispatch = service.createDispatch(portQName, SOAPMessage.class, Service.Mode.MESSAGE);
            
            System.out.println("DirectWSClient Dispatch客户端创建成功");
        }
        return dispatch;
    }
    
    /**
     * HelloWorld方法
     */
    public static String helloWorld() {
        try {
            SOAPMessage request = createHelloWorldMessage();
            Dispatch<SOAPMessage> client = getDispatch();
            SOAPMessage response = client.invoke(request);
            
            return extractTextFromSOAPMessage(response);
            
        } catch (Exception e) {
            throw new RuntimeException("HelloWorld调用失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询单个证书信息 - 直接返回CertificateInfo对象
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        try {
            System.out.println("DirectWSClient 正在查询证书信息...");
            
            SOAPMessage request = createQueryMessage(username, password, wzhgzbh, clsbdh);
            Dispatch<SOAPMessage> client = getDispatch();
            SOAPMessage response = client.invoke(request);
            
            // 解析SOAP响应为CertificateInfo对象
            return parseSOAPResponseToCertificateInfo(response);
            
        } catch (Exception e) {
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建HelloWorld SOAP消息
     */
    private static SOAPMessage createHelloWorldMessage() throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPBody body = envelope.getBody();
        SOAPElement helloWorldElement = body.addChildElement("HelloWorld", "tns");
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 创建查询SOAP消息
     */
    private static SOAPMessage createQueryMessage(String username, String password, String wzhgzbh, String clsbdh) throws Exception {
        MessageFactory messageFactory = MessageFactory.newInstance();
        SOAPMessage soapMessage = messageFactory.createMessage();
        SOAPPart soapPart = soapMessage.getSOAPPart();
        
        SOAPEnvelope envelope = soapPart.getEnvelope();
        envelope.addNamespaceDeclaration("tns", NAMESPACE_URI);
        
        SOAPBody body = envelope.getBody();
        
        // 创建QueryCertificateSingle元素
        SOAPElement queryElement = body.addChildElement("QueryCertificateSingle", "tns");
        
        // 添加参数
        SOAPElement usernameElement = queryElement.addChildElement("username", "tns");
        usernameElement.addTextNode(username);
        
        SOAPElement passwordElement = queryElement.addChildElement("password", "tns");
        passwordElement.addTextNode(password);
        
        SOAPElement wzhgzbhElement = queryElement.addChildElement("wzhgzbh", "tns");
        wzhgzbhElement.addTextNode(wzhgzbh);
        
        SOAPElement clsbdhElement = queryElement.addChildElement("clsbdh", "tns");
        clsbdhElement.addTextNode(clsbdh);
        
        soapMessage.saveChanges();
        return soapMessage;
    }
    
    /**
     * 解析SOAP响应为CertificateInfo对象列表
     */
    private static List<CertificateInfo> parseSOAPResponseToCertificateInfo(SOAPMessage response) throws Exception {
        List<CertificateInfo> result = new ArrayList<>();
        
        try {
            // 首先尝试直接解析SOAP响应
            SOAPBody responseBody = response.getSOAPBody();
            String responseText = extractTextFromSOAPBody(responseBody);
            
            System.out.println("DirectWSClient 收到响应，长度: " + responseText.length());
            
            // 使用智能解析方法
            CertificateInfo cert = parseResponseText(responseText);
            if (cert != null) {
                result.add(cert);
                System.out.println("DirectWSClient 成功解析出 1 条证书信息");
            } else {
                System.out.println("DirectWSClient 未能解析出有效的证书信息");
            }
            
        } catch (Exception e) {
            System.err.println("DirectWSClient 解析SOAP响应失败: " + e.getMessage());
            throw e;
        }
        
        return result;
    }
    
    /**
     * 智能解析响应文本为CertificateInfo对象
     * 支持多种响应格式：XML、JSON、纯文本
     */
    private static CertificateInfo parseResponseText(String responseText) {
        if (responseText == null || responseText.trim().isEmpty()) {
            return null;
        }

        try {
            initJAXBContext();

            // 1. 首先尝试XML格式解析（标准SOAP响应）
            CertificateInfo xmlResult = tryParseXML(responseText);
            if (xmlResult != null) {
                System.out.println("DirectWSClient 使用XML解析成功");
                return xmlResult;
            }

            // 2. 尝试JSON格式解析
            CertificateInfo jsonResult = tryParseJSON(responseText);
            if (jsonResult != null) {
                System.out.println("DirectWSClient 使用JSON解析成功");
                return jsonResult;
            }

            // 3. 最后使用文本解析（兜底方案）
            CertificateInfo textResult = tryParseText(responseText);
            if (textResult != null) {
                System.out.println("DirectWSClient 使用文本解析成功");
                return textResult;
            }

            System.out.println("DirectWSClient 所有解析方法都失败");
            return null;

        } catch (Exception e) {
            System.err.println("DirectWSClient 解析响应失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 尝试XML格式解析
     */
    private static CertificateInfo tryParseXML(String responseText) {
        try {
            // 检查是否包含XML标签
            if (!responseText.contains("<") || !responseText.contains(">")) {
                return null;
            }

            // 使用JAXB直接解析XML
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

            // 尝试解析为CertificateInfo对象
            // 这里需要根据实际的XML结构调整
            java.io.StringReader reader = new java.io.StringReader(responseText);
            Object result = unmarshaller.unmarshal(reader);

            if (result instanceof CertificateInfo) {
                return (CertificateInfo) result;
            } else if (result instanceof JAXBElement) {
                JAXBElement<?> element = (JAXBElement<?>) result;
                if (element.getValue() instanceof CertificateInfo) {
                    return (CertificateInfo) element.getValue();
                }
            }

            return null;

        } catch (Exception e) {
            // XML解析失败，不是错误，继续尝试其他格式
            return null;
        }
    }

    /**
     * 尝试JSON格式解析
     */
    private static CertificateInfo tryParseJSON(String responseText) {
        try {
            // 检查是否是JSON格式
            String trimmed = responseText.trim();
            if (!trimmed.startsWith("{") || !trimmed.endsWith("}")) {
                return null;
            }

            // 这里可以使用Jackson或其他JSON库解析
            // 由于项目中可能已有JSON依赖，这里提供框架
            ObjectFactory factory = new ObjectFactory();
            CertificateInfo cert = new CertificateInfo();

            // TODO: 实现JSON解析逻辑
            // 例如：ObjectMapper mapper = new ObjectMapper();
            // Map<String, Object> jsonMap = mapper.readValue(responseText, Map.class);
            // 然后根据字段映射设置cert的属性

            return null; // 暂时返回null，等待具体实现

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 文本格式解析（改进版）
     */
    private static CertificateInfo tryParseText(String responseText) {
        try {
            ObjectFactory factory = new ObjectFactory();
            CertificateInfo cert = new CertificateInfo();
            boolean hasData = false;

            // 多种分割方式尝试
            String[] parts = responseText.trim().split("\\s+");

            // 如果空格分割效果不好，尝试其他分隔符
            if (parts.length <= 2) {
                parts = responseText.trim().split("[,;|\\t]+");
            }

            // 使用更智能的字段识别
            for (String part : parts) {
                part = part.trim();
                if (part.isEmpty()) continue;

                // 证书编号识别（多种格式）
                if (isValidCertificateNumber(part)) {
                    cert.setWZHGZBH(factory.createCertificateInfoWZHGZBH(part));
                    hasData = true;
                    continue;
                }

                // VIN码识别
                if (isValidVIN(part)) {
                    cert.setCLSBDH(factory.createCertificateInfoCLSBDH(part));
                    hasData = true;
                    continue;
                }

                // 制造商识别
                if (isValidManufacturer(part)) {
                    cert.setCLZZQYMC(factory.createCertificateInfoCLZZQYMC(part));
                    hasData = true;
                    continue;
                }

                // 车辆类型识别
                if (isValidVehicleType(part)) {
                    cert.setCLLX(factory.createCertificateInfoCLLX(part));
                    hasData = true;
                    continue;
                }

                // 其他字段识别...
            }

            return hasData ? cert : null;

        } catch (Exception e) {
            System.err.println("DirectWSClient 文本解析失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证是否为有效的证书编号
     */
    private static boolean isValidCertificateNumber(String value) {
        return value != null &&
               (value.startsWith("HIDC") || value.startsWith("HGZB") || value.startsWith("WZ")) &&
               value.length() > 8;
    }

    /**
     * 验证是否为有效的VIN码
     */
    private static boolean isValidVIN(String value) {
        return value != null &&
               value.length() == 17 &&
               value.matches("[A-HJ-NPR-Z0-9]{17}"); // 标准VIN码格式
    }

    /**
     * 验证是否为有效的制造商名称
     */
    private static boolean isValidManufacturer(String value) {
        return value != null &&
               value.matches(".*[\\u4e00-\\u9fa5].*") &&
               value.length() > 2 &&
               (value.contains("汽车") || value.contains("制造") ||
                value.contains("有限公司") || value.contains("集团"));
    }

    /**
     * 验证是否为有效的车辆类型
     */
    private static boolean isValidVehicleType(String value) {
        return value != null &&
               (value.equals("专用汽车") || value.equals("载货汽车") ||
                value.equals("客车") || value.equals("轿车") ||
                value.equals("货车") || value.equals("乘用车"));
    }
    
    /**
     * 从SOAP消息中提取文本内容
     */
    private static String extractTextFromSOAPMessage(SOAPMessage message) throws Exception {
        SOAPBody body = message.getSOAPBody();
        return extractTextFromSOAPBody(body);
    }
    
    /**
     * 从SOAP Body中提取文本内容
     */
    private static String extractTextFromSOAPBody(SOAPBody body) throws Exception {
        StringBuilder result = new StringBuilder();
        extractTextFromNode(body, result);
        return result.toString().trim();
    }
    
    /**
     * 递归提取节点中的文本内容
     */
    private static void extractTextFromNode(org.w3c.dom.Node node, StringBuilder result) {
        if (node.getNodeType() == org.w3c.dom.Node.TEXT_NODE) {
            String text = node.getNodeValue().trim();
            if (!text.isEmpty()) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(text);
            }
        } else if (node.getNodeType() == org.w3c.dom.Node.ELEMENT_NODE) {
            org.w3c.dom.NodeList children = node.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                extractTextFromNode(children.item(i), result);
            }
        }
    }
    
    /**
     * 重置连接
     */
    public static synchronized void resetConnection() {
        dispatch = null;
        System.out.println("DirectWSClient 连接已重置");
    }
    
    /**
     * 验证连接
     */
    public static boolean validateConnection() {
        try {
            String result = helloWorld();
            return result != null && result.contains("Hello");
        } catch (Exception e) {
            return false;
        }
    }
}
