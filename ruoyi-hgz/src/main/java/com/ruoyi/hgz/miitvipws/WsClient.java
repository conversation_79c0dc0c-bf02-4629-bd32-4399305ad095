package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;

import javax.net.ssl.*;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

/**
 * Unified WebService Client
 * Integrates multiple connection strategies and provides a simple API interface
 *
 * <AUTHOR>
 */
public class WsClient {
    
    // Service configuration constants
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";

    // Timeout configuration
    private static final int CONNECT_TIMEOUT = 30000; // 30 seconds
    private static final int REQUEST_TIMEOUT = 60000; // 60 seconds

    // Singleton port instance
    private static volatile CertificateRequestVIP port;
    private static volatile boolean sslInitialized = false;
    
    /**
     * Get WebService port
     * Uses singleton pattern to ensure connection reuse
     *
     * @return CertificateRequestVIP port instance
     */
    public static synchronized CertificateRequestVIP getPort() {
        if (port == null) {
            try {
                // Initialize SSL configuration
                initSSL();

                // Create service port
                port = createServicePort();

                // Configure port properties
                configurePort(port);

            } catch (Exception e) {
                throw new RuntimeException("Failed to create WebService client: " + e.getMessage(), e);
            }
        }
        return port;
    }
    
    /**
     * Initialize SSL configuration
     * Trust all certificates, suitable for internal service calls
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }

        // Create TrustManager that trusts all certificates
        TrustManager[] trustAllCerts = new TrustManager[]{
            new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                @Override
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    // Trust all client certificates
                }

                @Override
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    // Trust all server certificates
                }
            }
        };

        // Initialize SSL context
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, trustAllCerts, new SecureRandom());

        // Set default SSL configuration
        HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
        
        sslInitialized = true;
    }
    
    /**
     * Create service port
     * Uses dynamic creation to avoid WSDL dependency issues
     */
    private static CertificateRequestVIP createServicePort() throws Exception {
        // Create QName for service and port
        QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
        QName portQName = new QName(NAMESPACE_URI, PORT_NAME);

        // Create service instance
        Service service = Service.create(serviceQName);
        service.addPort(portQName,
                       javax.xml.ws.soap.SOAPBinding.SOAP11HTTP_BINDING,
                       SERVICE_URL);

        // Get port
        return service.getPort(portQName, CertificateRequestVIP.class);
    }
    
    /**
     * Configure port properties
     * Set timeout and endpoint address
     */
    private static void configurePort(CertificateRequestVIP port) {
        BindingProvider bindingProvider = (BindingProvider) port;

        // Set endpoint address
        bindingProvider.getRequestContext().put(
            BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);

        // Set connection timeout
        bindingProvider.getRequestContext().put(
            "com.sun.xml.ws.connect.timeout", CONNECT_TIMEOUT);

        // Set request timeout
        bindingProvider.getRequestContext().put(
            "com.sun.xml.ws.request.timeout", REQUEST_TIMEOUT);
    }
    
    /**
     * Test connection
     *
     * @return Server response message
     * @throws RuntimeException when connection fails
     */
    public static String testConnection() {
        try {
            CertificateRequestVIP client = getPort();
            return client.helloWorld();
        } catch (Exception e) {
            throw new RuntimeException("Connection test failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Query single certificate information
     *
     * @param username Username
     * @param password Password
     * @param wzhgzbh Certificate number (can be empty)
     * @param clsbdh Vehicle identification number
     * @return Certificate information list
     * @throws RuntimeException when query fails
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password,
                                                               String wzhgzbh, String clsbdh) {
        try {
            CertificateRequestVIP client = getPort();
            return client.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        } catch (Exception e) {
            throw new RuntimeException("Query certificate information failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Query certificate information by conditions
     *
     * @param username Username
     * @param password Password
     * @param wzhgzbh Certificate number
     * @param clsbdh Vehicle identification number
     * @param clxh Vehicle model
     * @param status Status
     * @param startTime Start time
     * @param endTime End time
     * @param pagesite Page number
     * @param pageSize Page size
     * @return Certificate information list
     * @throws RuntimeException when query fails
     */
    public static List<CertificateInfo> queryByCondition(String username, String password,
                                                         String wzhgzbh, String clsbdh, String clxh,
                                                         String status, String startTime, String endTime,
                                                         int pagesite, int pageSize) {
        try {
            CertificateRequestVIP client = getPort();
            return client.queryByCondition(username, password, wzhgzbh, clsbdh, clxh,
                                         status, startTime, endTime, pagesite, pageSize);
        } catch (Exception e) {
            throw new RuntimeException("Query certificate information by condition failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * Reset connection
     * Clear cached port instance and force reconnection
     */
    public static synchronized void resetConnection() {
        port = null;
    }
}
