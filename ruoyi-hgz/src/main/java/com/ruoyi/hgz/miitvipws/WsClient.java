package com.ruoyi.hgz.miitvipws;

import com.ruoyi.hgz.miitvipws.client.CertificateInfo;
import com.ruoyi.hgz.miitvipws.client.CertificateRequestVIP;

import javax.net.ssl.*;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.List;

/**
 * 统一的WebService客户端
 * 整合多种连接策略，提供简洁的API接口
 * 
 * <AUTHOR>
 */
public class WsClient {
    
    // 服务配置常量
    private static final String SERVICE_URL = "https://hgz.miit.gov.cn/enterprise/services/CertificateRequestVIPService";
    private static final String NAMESPACE_URI = "http://www.vidc.info/certificate/operation/";
    private static final String SERVICE_NAME = "CertificateRequestVIP";
    private static final String PORT_NAME = "CertificateRequestVIPServiceImplPort";
    
    // 超时配置
    private static final int CONNECT_TIMEOUT = 30000; // 30秒
    private static final int REQUEST_TIMEOUT = 60000; // 60秒
    
    // 单例端口实例
    private static volatile CertificateRequestVIP port;
    private static volatile boolean sslInitialized = false;
    
    /**
     * 获取WebService端口
     * 使用单例模式，确保连接复用
     * 
     * @return CertificateRequestVIP 端口实例
     */
    public static synchronized CertificateRequestVIP getPort() {
        if (port == null) {
            try {
                // 初始化SSL配置
                initSSL();
                
                // 创建服务端口
                port = createServicePort();
                
                // 配置端口属性
                configurePort(port);
                
            } catch (Exception e) {
                throw new RuntimeException("创建WebService客户端失败: " + e.getMessage(), e);
            }
        }
        return port;
    }
    
    /**
     * 初始化SSL配置
     * 信任所有证书，适用于内部服务调用
     */
    private static synchronized void initSSL() throws Exception {
        if (sslInitialized) {
            return;
        }
        
        // 创建信任所有证书的TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
            new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                
                @Override
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    // 信任所有客户端证书
                }
                
                @Override
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    // 信任所有服务器证书
                }
            }
        };
        
        // 初始化SSL上下文
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, trustAllCerts, new SecureRandom());
        
        // 设置默认SSL配置
        HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
        
        sslInitialized = true;
    }
    
    /**
     * 创建服务端口
     * 使用动态方式创建，避免WSDL依赖问题
     */
    private static CertificateRequestVIP createServicePort() throws Exception {
        // 创建服务和端口的QName
        QName serviceQName = new QName(NAMESPACE_URI, SERVICE_NAME);
        QName portQName = new QName(NAMESPACE_URI, PORT_NAME);
        
        // 创建服务实例
        Service service = Service.create(serviceQName);
        service.addPort(portQName, 
                       javax.xml.ws.soap.SOAPBinding.SOAP11HTTP_BINDING, 
                       SERVICE_URL);
        
        // 获取端口
        return service.getPort(portQName, CertificateRequestVIP.class);
    }
    
    /**
     * 配置端口属性
     * 设置超时时间和端点地址
     */
    private static void configurePort(CertificateRequestVIP port) {
        BindingProvider bindingProvider = (BindingProvider) port;
        
        // 设置端点地址
        bindingProvider.getRequestContext().put(
            BindingProvider.ENDPOINT_ADDRESS_PROPERTY, SERVICE_URL);
        
        // 设置连接超时
        bindingProvider.getRequestContext().put(
            "com.sun.xml.ws.connect.timeout", CONNECT_TIMEOUT);
        
        // 设置请求超时
        bindingProvider.getRequestContext().put(
            "com.sun.xml.ws.request.timeout", REQUEST_TIMEOUT);
    }
    
    /**
     * 测试连接
     * 
     * @return 服务器响应消息
     * @throws RuntimeException 连接失败时抛出
     */
    public static String testConnection() {
        try {
            CertificateRequestVIP client = getPort();
            return client.helloWorld();
        } catch (Exception e) {
            throw new RuntimeException("连接测试失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询单个证书信息
     * 
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号（可为空）
     * @param clsbdh 车辆识别代号
     * @return 证书信息列表
     * @throws RuntimeException 查询失败时抛出
     */
    public static List<CertificateInfo> queryCertificateSingle(String username, String password, 
                                                               String wzhgzbh, String clsbdh) {
        try {
            CertificateRequestVIP client = getPort();
            return client.queryCertificateSingle(username, password, wzhgzbh, clsbdh);
        } catch (Exception e) {
            throw new RuntimeException("查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 按条件查询证书信息
     * 
     * @param username 用户名
     * @param password 密码
     * @param wzhgzbh 网证合格证编号
     * @param clsbdh 车辆识别代号
     * @param clxh 车辆型号
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pagesite 页码
     * @param pageSize 页大小
     * @return 证书信息列表
     * @throws RuntimeException 查询失败时抛出
     */
    public static List<CertificateInfo> queryByCondition(String username, String password,
                                                         String wzhgzbh, String clsbdh, String clxh,
                                                         String status, String startTime, String endTime,
                                                         int pagesite, int pageSize) {
        try {
            CertificateRequestVIP client = getPort();
            return client.queryByCondition(username, password, wzhgzbh, clsbdh, clxh, 
                                         status, startTime, endTime, pagesite, pageSize);
        } catch (Exception e) {
            throw new RuntimeException("按条件查询证书信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 重置连接
     * 清除缓存的端口实例，强制重新创建连接
     */
    public static synchronized void resetConnection() {
        port = null;
    }
}
